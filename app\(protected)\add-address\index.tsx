import React from "react";
import { KeyboardAvoidingView, Platform } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";
import { Header, AddAddressForm } from "@/components";
import Toast from "react-native-toast-message";
import {
  addUpdateAddressAction,
  AddressResponse,
} from "@/store/actions/address";
import { getUserDetailsAction } from "@/store/actions/auth";
import { Content as Container } from "@/styles/Profile.styles";
import FormTemplate from "@/template/FormTemplate";

const GSTIN_REGEX = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][A-Z0-9]Z[A-Z0-9]$/;

export interface AddAddressFormData {
  billing_shipping: number;
  contact_person_name: string;
  company_name: string;
  address_line_one: string;
  address_line_two: string;
  gst_number: string;
  post_code: string;
  country_id: number;
  state_id: number;
  city_id: number;
}
const addressSchema = yup.object().shape({
  billing_shipping: yup
    .number()
    .required("Address type is required")
    .oneOf([1, 2], "Address type must be either Billing or Shipping"),

  contact_person_name: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value)),

  company_name: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value)),

  address_line_one: yup
    .string()
    .required("Address line 1 is required")
    .min(5, "Address line 1 must be at least 5 characters")
    .max(100, "Address line 1 must not exceed 100 characters"),

  address_line_two: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value))
    .min(5, "Address line 2 must be at least 5 characters")
    .max(100, "Address line 2 must not exceed 100 characters"),

  gst_number: yup
    .string()
    .nullable()
    .transform((value) => (value === "" ? null : value))
    .test("gst", "Please enter a valid GSTIN number", (value) => {
      if (!value) return true;
      return GSTIN_REGEX.test(value);
    }),

  post_code: yup
    .string()
    .required("PIN code is required")
    .matches(/^\d{6}$/, "PIN code must be exactly 6 digits"),

  country_id: yup
    .number()
    .required("Country is required")
    .min(1, "Country is required"),

  state_id: yup
    .number()
    .required("State is required")
    .min(1, "State is required"),

  city_id: yup.number().required("City is required").min(1, "City is required"),
});

const AddAddressScreen = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { theme } = useTheme();
  const params = useLocalSearchParams();
  const vendorId = params.vendorId ? parseInt(params.vendorId as string) : null;
  const addressId = params.address_id
    ? parseInt(params.address_id as string)
    : null;
  const isEditMode = !!addressId;

  const { user, countryList } = useAppSelector(
    (state: RootState) => state.auth
  );
  const { vendorList } = useAppSelector((state: RootState) => state.vendor);
  const vendor = vendorList.find((v) => v.vendor_id === vendorId);
  const countryId = countryList.find(
    (c) => c.country_code_alpha === vendor?.country_code_alpha
  )?.id;

  const goBack = () => {
    router.back();
  };
  const onSubmit = async (data: AddAddressFormData) => {
    try {
      let response: AddressResponse;

      if (isEditMode && addressId) {
        // Update existing address
        response = await dispatch(
          addUpdateAddressAction({
            ...data,
            address_id: addressId,
          })
        ).unwrap();
      } else {
        // Add new address
        response = await dispatch(addUpdateAddressAction(data)).unwrap();
      }

      if (response.status) {
        Toast.show({
          type: "success",
          text1:
            response.message ||
            `Address ${isEditMode ? "updated" : "added"} successfully`,
        });

        // Refresh user details to get updated address list
        await dispatch(getUserDetailsAction({}));

        goBack();
      }
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1:
          error?.message ||
          `Failed to ${isEditMode ? "update" : "add"} address`,
      });
    }
  };

  // Get existing address data for edit mode
  const getDefaultValues = () => {
    if (isEditMode && addressId && user?.address) {
      const existingAddress = user.address.find(
        (addr: any) => addr.id === addressId
      );

      if (existingAddress) {
        return {
          billing_shipping: parseInt(existingAddress.billing_shipping) || 1,
          contact_person_name: existingAddress.contact_person_name || "",
          company_name: existingAddress.company_name || "",
          address_line_one: existingAddress.address_line_one || "",
          address_line_two: existingAddress.address_line_two || "",
          gst_number: existingAddress.gst_number || "",
          post_code: existingAddress.post_code || "",
          country_id: existingAddress.country_id || 0,
          state_id: existingAddress.state_id || 0,
          city_id: existingAddress.city_id || 0,
        };
      }
    }

    return {
      billing_shipping: 1, // Default to Billing
      contact_person_name: vendor?.name || "",
      company_name: "",
      address_line_one: vendor?.address_line_one || "",
      address_line_two: vendor?.address_line_two || "",
      gst_number: vendor?.gst_number || "",
      post_code: vendor?.post_code || "",
      country_id: countryId || 0,
      state_id: vendor?.state_id || 0,
      city_id: vendor?.city_id || 0,
    };
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: theme.colors.background }}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <Header
        title={isEditMode ? "Edit Address" : "Add Address"}
        showCart={false}
        showBack
        onBackPress={goBack}
      />

      <Container>
        <FormTemplate<AddAddressFormData>
          Component={(props) => (
            <AddAddressForm {...props} isEditMode={isEditMode} />
          )}
          onSubmit={onSubmit}
          defaultValues={getDefaultValues()}
          resolver={yupResolver(addressSchema)}
          mode="onChange"
        />
      </Container>
    </KeyboardAvoidingView>
  );
};

export default AddAddressScreen;
