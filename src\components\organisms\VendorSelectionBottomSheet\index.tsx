import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetFlatList, BottomSheetModal } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { VendorDetails } from "@/types/vendor";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import {
  Title,
  SearchContainer,
  SearchInput,
  VendorItem,
  VendorInfo,
  VendorName,
  VendorId,
  EmptyContainer,
  EmptyText,
  StyledBottomSheetView,
  ItemsCountText,
} from "./styles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";

interface VendorSelectionBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (vendor: VendorDetails) => void;
  vendors: VendorDetails[];
  isLoading?: boolean;
}

const VendorSelectionBottomSheet: React.FC<VendorSelectionBottomSheetProps> = ({
  isVisible,
  onClose,
  onSelect,
  vendors: vendorList,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState("");
  const [hasMoreData, setHasMoreData] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const vendors = Array.isArray(vendorList) ? vendorList : [];
  const hasVendors = vendors.length > 0;
  const [filteredVendors, setFilteredVendors] =
    useState<VendorDetails[]>(vendors);

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  useEffect(() => {
    if (!query) {
      setFilteredVendors(vendors);
      return;
    }

    setFilteredVendors(
      vendors.filter((vendor) => {
        const fullName =
          `${vendor.first_name} ${vendor.last_name}`.toLowerCase();
        const searchQuery = query.toLowerCase();
        return (
          fullName.includes(searchQuery) ||
          vendor.vendor_id?.toString().includes(searchQuery) ||
          vendor.email?.toLowerCase().includes(searchQuery)
        );
      })
    );
  }, [query, vendors]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["70%"], []);

  const handleSearch = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const handleLoadMore = useCallback(async () => {
    if (!hasMoreData || isLoadingMore) return;

    setIsLoadingMore(true);
    // TODO: Implement load more logic
    setIsLoadingMore(false);
  }, []);

  const handleVendorSelect = useCallback(
    (vendor: VendorDetails) => {
      onSelect(vendor);
      onClose();
    },
    [onSelect, onClose]
  );

  const renderVendorItem = useCallback(
    ({ item }: { item: VendorDetails }) => (
      <VendorItem onPress={() => handleVendorSelect(item)}>
        <VendorInfo>
          <VendorName>{item.first_name + " " + item.last_name}</VendorName>
          {item.email && <VendorId>{item.email}</VendorId>}
        </VendorInfo>
      </VendorItem>
    ),
    [handleVendorSelect]
  );

  const renderEmptyComponent = useCallback(() => {
    if (isLoading) {
      return <LoadingOverlay isLoading={true} size="large" />;
    }

    return (
      <EmptyContainer>
        <EmptyText>
          {query
            ? t("common.no_results_found")
            : t("common.no_vendors_available")}
        </EmptyText>
      </EmptyContainer>
    );
  }, [isLoading, query, t]);

  const renderBackdrop = useBottomSheetBackdrop();

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backdropComponent={renderBackdrop}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      enableDynamicSizing={false}
      enablePanDownToClose
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <StyledBottomSheetView>
        <Title>{t("cart.selectVendor")}</Title>

        <SearchContainer>
          <Ionicons name="search" size={20} color={theme.colors.text} />
          <SearchInput
            placeholder={t("cart.searchVendor")}
            value={query}
            onChangeText={handleSearch}
            placeholderTextColor={theme.colors.text}
          />
        </SearchContainer>

        <ItemsCountText>
          {vendors.length} {t("vendorManagement.vendors")}
        </ItemsCountText>

        <BottomSheetFlatList
          data={filteredVendors}
          keyExtractor={(item) => item.id?.toString() || item.mobile.toString()}
          renderItem={renderVendorItem}
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          showsVerticalScrollIndicator={false}
        />
      </StyledBottomSheetView>
    </BottomSheetModal>
  );
};

export default VendorSelectionBottomSheet;
