import React, { useState, useEffect, useCallback, Fragment } from "react";
import { useTranslation } from "react-i18next";
import { router, useFocusEffect } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";
import { useDebounce } from "@/utils/useDebounce";
import {
  getVendorListAction,
  deleteVendorAction,
} from "@/store/actions/vendor";
import { VendorDetails } from "@/types/vendor";
import { Header, LoadingOverlay } from "@/components";
import { FloatingActionButton } from "@/components/atoms";
import { ConfirmationModal, SearchBar } from "@/components/molecules";
import SortBottomSheet, {
  SortOptionList,
} from "@/components/organisms/SortBottomSheet";
import { vendorSortOptions, vendorStatusList } from "@/utils/common";
import Toast from "react-native-toast-message";
import {
  Container,
  Content,
  SearchContainer,
  VendorListContainer,
  EmptyContainer,
  EmptyText,
  EmptyIcon,
  EmptySubtext,
  InfoRow,
  ItemsCountText,
  SortButtonRow,
  SortLabel,
  SortValueText,
  ActiveFiltersContainer,
  FilterChipsRow,
  ActiveFilterChip,
  ActiveFilterText,
} from "@/styles/VendorManagement.styles";
import { VendorList } from "@/components/organisms";
import {
  deleteVendor,
  resetFilters,
  resetVendorForm,
} from "@/store/slices/vendorSlice";
import StatusFilterBottomSheet from "@/components/organisms/StatusFilterBottomSheet";

export default function VendorManagementScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const {
    vendorList,
    isVendorListLoading,
    isLoadingMore,
    isDeleteLoading,
    filters,
  } = useAppSelector((state) => state.vendor);
  const [searchQuery, setSearchQuery] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<VendorDetails | null>(
    null
  );
  const [sortSheetVisible, setSortSheetVisible] = useState(false);
  const [selectedSort, setSelectedSort] = useState<SortOptionList | null>(null);
  const [statusSheetVisible, setStatusSheetVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  // Add debounced search
  const debouncedSearch = useDebounce(searchQuery, 300);

  const loadVendors = useCallback(
    async (
      searchTerm?: string,
      sortParams?: {
        order_by?: number;
        sort_by?: number;
        auth_dealer?: number;
      },
      page: number = 1
    ) => {
      try {
        const payload = {
          page,
          ...(searchTerm ? { search: searchTerm } : {}),
          ...(sortParams ? sortParams : {}),
        };
        const response = await dispatch(getVendorListAction(payload)).unwrap();

        // Check if there's more data
        if (response.pagination) {
          setHasMoreData(page < response.pagination.last_page);
        }

        return response;
      } catch (error) {
        console.log("error", error);
        Toast.show({
          type: "error",
          text1: error?.message || t("vendorManagement.loadError"),
        });
        throw error;
      }
    },
    [dispatch]
  );

  const handleSearch = useCallback(
    async (query: string) => {
      setCurrentPage(1);
      setHasMoreData(true);
      await loadVendors(
        query,
        {
          order_by: selectedSort?.order_by,
          sort_by: selectedSort?.sort_by,
          auth_dealer: selectedStatus,
        },
        1
      );
    },
    [loadVendors, selectedSort, selectedStatus]
  );

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    setCurrentPage(1);
    setHasMoreData(true);
    setSearchQuery("");
    setSelectedSort(null);
    setSelectedStatus(null);
    dispatch(resetFilters());
    try {
      await loadVendors();
    } finally {
      setIsRefreshing(false);
    }
  }, [loadVendors]);

  const handleLoadMore = useCallback(async () => {
    if (!hasMoreData || isLoadingMore) return;

    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);

    try {
      await loadVendors(
        searchQuery,
        {
          order_by: selectedSort?.order_by,
          sort_by: selectedSort?.sort_by,
          auth_dealer: selectedStatus,
        },
        nextPage
      );
    } catch (error) {
      // Revert page number on error
      setCurrentPage(currentPage);
    }
  }, [
    hasMoreData,
    isLoadingMore,
    currentPage,
    loadVendors,
    searchQuery,
    selectedSort,
    selectedStatus,
  ]);

  useEffect(() => {
    loadVendors();
  }, []);

  // Handle debounced search
  useEffect(() => {
    if (debouncedSearch && debouncedSearch.length >= 3) {
      handleSearch(debouncedSearch);
    } else if (debouncedSearch === "") {
      handleSearch("");
    }
  }, [debouncedSearch, handleSearch]);

  useFocusEffect(
    useCallback(() => {
      // Reset vendor form data when returning to this screen to ensure clean forms
      dispatch(resetVendorForm());
    }, [dispatch])
  );

  const handleSearchInputChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleAddVendor = useCallback(() => {
    router.push("/addvendor");
  }, []);

  const handleEditVendor = useCallback(
    async (vendor: VendorDetails) => {
      setSearchQuery("");
      setSelectedSort(null);
      setSelectedStatus(null);
      dispatch(resetFilters());
      router.push(`/addvendor?vendorId=${vendor.vendor_id}`);
    },
    [router, dispatch]
  );

  const handleDeleteVendor = useCallback((vendor: VendorDetails) => {
    setSelectedVendor(vendor);
    setShowDeleteModal(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!selectedVendor?.vendor_id) return;
    try {
      const response = await dispatch(
        deleteVendorAction({ vendor_id: selectedVendor.vendor_id })
      ).unwrap();
      if (response.status) {
        dispatch(deleteVendor({ id: selectedVendor.vendor_id }));
        Toast.show({
          type: "success",
          text1: response.message || t("vendorManagement.deleteSuccess"),
        });
        setShowDeleteModal(false);
        setSelectedVendor(null);
      } else {
        Toast.show({
          type: "error",
          text1: response.message || t("vendorManagement.deleteError"),
        });
      }
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || t("vendorManagement.deleteError"),
      });
    }
  }, [selectedVendor, dispatch]);

  const renderEmptyState = () => (
    <EmptyContainer>
      <EmptyIcon>
        <Ionicons
          name="people-outline"
          size={80}
          color={theme.colors.secondary}
        />
      </EmptyIcon>
      <EmptyText>
        {searchQuery
          ? t("vendorManagement.noSearchResults")
          : t("vendorManagement.noVendors")}
      </EmptyText>
      <EmptySubtext>
        {searchQuery
          ? t("vendorManagement.tryDifferentSearch")
          : t("vendorManagement.addFirstVendor")}
      </EmptySubtext>
    </EmptyContainer>
  );

  const handleStatusPress = () => setStatusSheetVisible(true);

  const handleStatusDismiss = () => setStatusSheetVisible(false);

  const handleStatusClear = () => {
    setSelectedStatus(null);
    setCurrentPage(1);
    setHasMoreData(true);
    setStatusSheetVisible(false);
    loadVendors(searchQuery, {
      order_by: selectedSort?.order_by,
      sort_by: selectedSort?.sort_by,
    });
  };

  const handleStatusApply = async (statusId: number | null) => {
    setSelectedStatus(statusId);
    setCurrentPage(1);
    setHasMoreData(true);
    setStatusSheetVisible(false);
    loadVendors(searchQuery, {
      order_by: selectedSort?.order_by,
      sort_by: selectedSort?.sort_by,
      auth_dealer: statusId,
    });
  };

  // Sort handlers
  const handleSortPress = () => setSortSheetVisible(true);

  const handleSortClear = () => {
    setSelectedSort(null);
    setCurrentPage(1);
    setHasMoreData(true);
    setSortSheetVisible(false);
    loadVendors(searchQuery, {
      order_by: undefined,
      sort_by: undefined,
      auth_dealer: selectedStatus,
    });
  };

  const handleSortDismiss = () => setSortSheetVisible(false);

  const handleSortApply = (sortType: SortOptionList | null) => {
    setSelectedSort(sortType);
    setCurrentPage(1);
    setHasMoreData(true);
    setSortSheetVisible(false);
    loadVendors(searchQuery, {
      order_by: sortType?.order_by,
      sort_by: sortType?.sort_by,
      auth_dealer: selectedStatus,
    });
  };

  const vendors = Array.isArray(vendorList) ? vendorList : [];
  const hasVendors = vendors.length > 0;
  const renderActiveFilters = () => {
    const activeFilters = [];

    if (selectedStatus !== null) {
      const status = vendorStatusList.find((s) => s.id === selectedStatus);
      if (status) {
        activeFilters.push({
          label: status.label,
          onRemove: () => handleStatusApply(null),
        });
      }
    }

    if (activeFilters.length === 0) return null;

    return (
      <ActiveFiltersContainer>
        <FilterChipsRow>
          {activeFilters.map((filter, index) => (
            <ActiveFilterChip key={index} onPress={filter.onRemove}>
              <ActiveFilterText>{filter.label}</ActiveFilterText>
              <Ionicons name="close" size={14} color={theme.colors.white} />
            </ActiveFilterChip>
          ))}
        </FilterChipsRow>
      </ActiveFiltersContainer>
    );
  };

  return (
    <Fragment>
      <Header title={t("vendorManagement.title")} showBack={true} />

      <Container>
        <SearchContainer>
          <SearchBar
            onSearch={handleSearchInputChange}
            onFilterPress={handleStatusPress}
            value={searchQuery}
          />
        </SearchContainer>

        {/* Info Row with Sort */}
        {hasVendors && (
          <InfoRow>
            <ItemsCountText>
              {vendors.length} {t("vendorManagement.vendors")}
            </ItemsCountText>
            <SortButtonRow onPress={handleSortPress}>
              <SortLabel>{t("Sort by")}: </SortLabel>
              <SortValueText>
                {selectedSort?.label || t("Select")}
              </SortValueText>
              <Ionicons
                name="chevron-down"
                size={16}
                color={theme.colors.primary}
                style={{ marginLeft: 2 }}
              />
            </SortButtonRow>
          </InfoRow>
        )}

        {renderActiveFilters()}

        <Content>
          {isVendorListLoading && !isRefreshing ? (
            <LoadingOverlay isLoading={true} size="large" />
          ) : hasVendors ? (
            <VendorListContainer>
              <VendorList
                vendors={vendors}
                onEdit={handleEditVendor}
                onDelete={handleDeleteVendor}
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                onEndReached={handleLoadMore}
                loading={isVendorListLoading}
                loadingMore={isLoadingMore}
              />
            </VendorListContainer>
          ) : (
            renderEmptyState()
          )}
        </Content>
      </Container>

      <FloatingActionButton onPress={handleAddVendor} icon="add" />

      <ConfirmationModal
        visible={showDeleteModal}
        title={t("vendorManagement.deleteTitle")}
        message={t("vendorManagement.deleteMessage", {
          name: `${selectedVendor?.first_name} ${selectedVendor?.last_name}`,
        })}
        variant="danger"
        icon="trash-outline"
        confirmText={t("common.delete")}
        cancelText={t("common.cancel")}
        onCancel={() => {
          setShowDeleteModal(false);
          setSelectedVendor(null);
        }}
        onConfirm={confirmDelete}
        loading={isDeleteLoading}
      />

      {/* Sort Bottom Sheet */}
      <SortBottomSheet
        isVisible={sortSheetVisible}
        onClose={handleSortDismiss}
        onApply={handleSortApply}
        onDismiss={handleSortDismiss}
        onClear={handleSortClear}
        sortOptions={vendorSortOptions}
        title={t("Sort by")}
      />
      {/* Status Filter Bottom Sheet */}
      <StatusFilterBottomSheet
        filters={filters}
        isVisible={statusSheetVisible}
        onDismiss={handleStatusDismiss}
        statusOptions={vendorStatusList}
        onClear={handleStatusClear}
        onApply={handleStatusApply}
        selectedkey={"auth_dealer"}
      />
    </Fragment>
  );
}
