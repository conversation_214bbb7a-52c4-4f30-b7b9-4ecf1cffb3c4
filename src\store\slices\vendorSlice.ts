import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { VendorDetails } from "../../types/vendor";
import { getVendorListAction, deleteVendorAction } from "../actions/vendor";
import { logoutAction } from "../actions/auth";
import { logoutLocalAction } from "../actions/auth";

interface VendorState {
  vendor: VendorDetails | null;
  vendorList: VendorDetails[] | null;
  isLoading: boolean;
  isVendorListLoading: boolean;
  isLoadingMore: boolean;
  isDeleteLoading: boolean;
  filters: any;
}

const initialState: VendorState = {
  vendor: null,
  vendorList: null,
  isLoading: false,
  isVendorListLoading: false,
  isLoadingMore: false,
  isDeleteLoading: false,
  filters: {},
};

const vendorSlice = createSlice({
  name: "vendor",
  initialState,
  reducers: {
    setVendor: (state, action: PayloadAction<VendorDetails>) => {
      state.vendor = action.payload;
    },
    resetVendor: (state) => {
      state.vendor = null;
      state.vendorList = null;
    },
    resetVendorForm: (state) => {
      state.vendor = null;
    },
    resetFilters: (state) => {
      state.filters = {};
    },
    updateVendor: (state, action: PayloadAction<Partial<VendorDetails>>) => {
      if (state.vendor) {
        state.vendor = { ...state.vendor, ...action.payload };
      }
    },
    deleteVendor: (state, action: PayloadAction<{ id: number }>) => {
      if (state.vendorList) {
        state.vendorList = state.vendorList.filter(
          (vendor) => vendor.vendor_id !== action.payload.id
        );
      }
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getVendorListAction.pending, (state, action) => {
      // Check if this is pagination (page > 1) or initial load
      const isPagination = action.meta.arg?.page && action.meta.arg.page > 1;
      if (isPagination) {
        state.isLoadingMore = true;
      } else {
        state.isVendorListLoading = true;
      }
    });
    builder.addCase(getVendorListAction.fulfilled, (state, action) => {
      // Vendor list fetch
      state.isVendorListLoading = false;
      state.isLoadingMore = false;
      state.filters = action.payload.meta;

      // Handle pagination - if page is 1, replace the list, otherwise append
      const currentPage = action.payload.meta?.page || 1;
      if (currentPage === 1) {
        state.vendorList = action.payload.data;
      } else {
        // Append new data to existing list
        state.vendorList = state.vendorList
          ? [...state.vendorList, ...action.payload.data]
          : action.payload.data;
      }
    });
    builder.addCase(getVendorListAction.rejected, (state) => {
      state.isVendorListLoading = false;
      state.isLoadingMore = false;
    });
    // Delete vendor
    builder.addCase(deleteVendorAction.pending, (state) => {
      state.isDeleteLoading = true;
    });
    builder.addCase(deleteVendorAction.fulfilled, (state, action) => {
      state.isDeleteLoading = false;
      // Remove the deleted vendor from the list
      if (state.vendorList && action.meta.arg) {
        state.vendorList = state.vendorList.filter(
          (vendor) => vendor.vendor_id !== action.meta.arg.vendor_id
        );
      }
    });
    // Logout
    builder.addCase(logoutAction.fulfilled, () => {
      return initialState;
    });
    // Logout Local
    builder.addCase(logoutLocalAction.fulfilled, () => {
      return initialState;
    });
    builder.addCase(deleteVendorAction.rejected, (state) => {
      state.isDeleteLoading = false;
    });
  },
});

export const {
  setVendor,
  updateVendor,
  resetVendor,
  resetVendorForm,
  deleteVendor,
  resetFilters,
} = vendorSlice.actions;
export default vendorSlice.reducer;
